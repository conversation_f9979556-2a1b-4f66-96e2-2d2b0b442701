import crypto from 'crypto';
import { ResponseUtil  } from '../utils/response.js';
import logger from '../config/logger.js';

/**
 * CSRF Protection Middleware
 */
class CSRFProtection {
  static tokens = new Map();
  static TOKEN_EXPIRY = 60 * 60 * 1000; // 1 hour

  /**
   * Generate CSRF token
   * @param {string} sessionId - Session ID
   * @returns {string} CSRF token
   */
  static generateToken(sessionId) {
    const token = crypto.randomBytes(32).toString('hex');
    const expires = Date.now() + this.TOKEN_EXPIRY;
    
    this.tokens.set(sessionId, { token, expires });
    
    // Clean up expired tokens
    this.cleanupExpiredTokens();
    
    return token;
  }

  /**
   * Verify CSRF token
   * @param {string} sessionId - Session ID
   * @param {string} token - CSRF token to verify
   * @returns {boolean} True if token is valid
   */
  static verifyToken(sessionId, token) {
    const storedToken = this.tokens.get(sessionId);
    
    if (!storedToken) {
      return false;
    }

    if (Date.now() > storedToken.expires) {
      this.tokens.delete(sessionId);
      return false;
    }

    return storedToken.token === token;
  }

  /**
   * Clean up expired tokens
   */
  static cleanupExpiredTokens() {
    const now = Date.now();
    for (const [sessionId, tokenData] of this.tokens.entries()) {
      if (now > tokenData.expires) {
        this.tokens.delete(sessionId);
      }
    }
  }

  /**
   * Middleware to get CSRF token
   * @returns {Function} Middleware function
   */
  static getToken() {
    return (req, res, next) => {
      try {
        const sessionId = req.sessionId || req.headers['x-session-id'];
        
        if (!sessionId) {
          ResponseUtil.badRequest(res, 'Session ID is required');
          return;
        }

        const token = this.generateToken(sessionId);
        
        ResponseUtil.success(res, 'CSRF token generated', { 
          csrfToken: token,
          sessionId: sessionId 
        });
      } catch (error) {
        logger.error('Error generating CSRF token:', error);
        ResponseUtil.error(res, 'Failed to generate CSRF token');
      }
    };
  }

  /**
   * Middleware to protect against CSRF attacks
   * @returns {Function} Middleware function
   */
  static protect() {
    return (req, res, next) => {
      try {
        // Skip CSRF protection for GET requests
        if (req.method === 'GET') {
          next();
          return;
        }

        // Skip CSRF protection in development
        if (process.env.NODE_ENV === 'development') {
          next();
          return;
        }

        const sessionId = req.sessionId || req.headers['x-session-id'];
        const csrfToken = req.headers['x-csrf-token'] || req.body.csrfToken;

        if (!sessionId) {
          logger.warn('CSRF protection: Missing session ID');
          ResponseUtil.forbidden(res, 'Session ID is required');
          return;
        }

        if (!csrfToken) {
          logger.warn('CSRF protection: Missing CSRF token');
          ResponseUtil.forbidden(res, 'CSRF token is required');
          return;
        }

        if (!this.verifyToken(sessionId, csrfToken)) {
          logger.warn('CSRF protection: Invalid CSRF token');
          ResponseUtil.forbidden(res, 'Invalid CSRF token');
          return;
        }

        next();
      } catch (error) {
        logger.error('CSRF protection error:', error);
        ResponseUtil.forbidden(res, 'CSRF protection failed');
      }
    };
  }
}

/**
 * Request signing middleware for API security
 */
class RequestSigning {
  static SECRET = process.env.CSRF_SECRET || 'your_csrf_secret_here';

  /**
   * Generate signature for request
   * @param {string} method - HTTP method
   * @param {string} url - Request URL
   * @param {string} body - Request body
   * @param {string} timestamp - Request timestamp
   * @param {string} nonce - Request nonce
   * @returns {string} Request signature
   */
  static generateSignature(method, url, body, timestamp, nonce) {
    const payload = `${method}|${url}|${body}|${timestamp}|${nonce}`;
    return crypto
      .createHmac('sha256', this.SECRET)
      .update(payload)
      .digest('hex');
  }

  /**
   * Verify request signature
   * @param {string} method - HTTP method
   * @param {string} url - Request URL
   * @param {string} body - Request body
   * @param {string} timestamp - Request timestamp
   * @param {string} nonce - Request nonce
   * @param {string} signature - Signature to verify
   * @returns {boolean} True if signature is valid
   */
  static verifySignature(method, url, body, timestamp, nonce, signature) {
    const expectedSignature = this.generateSignature(method, url, body, timestamp, nonce);
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    );
  }

  /**
   * Middleware to verify signed requests
   * @returns {Function} Middleware function
   */
  static verify() {
    return (req, res, next) => {
      try {
        // Skip signature verification for development
        if (process.env.NODE_ENV === 'development') {
          next();
          return;
        }

        const signature = req.headers['x-signature'];
        const timestamp = req.headers['x-timestamp'];
        const nonce = req.headers['x-nonce'];

        if (!signature || !timestamp || !nonce) {
          logger.warn('Missing signature headers');
          ResponseUtil.forbidden(res, 'Request signature required');
          return;
        }

        // Check timestamp (prevent replay attacks)
        const requestTime = parseInt(timestamp);
        const currentTime = Date.now();
        const timeDiff = Math.abs(currentTime - requestTime);
        
        if (timeDiff > 5 * 60 * 1000) { // 5 minutes
          logger.warn('Request timestamp too old');
          ResponseUtil.forbidden(res, 'Request timestamp invalid');
          return;
        }

        // Verify signature
        const body = JSON.stringify(req.body || {});
        const isValid = this.verifySignature(
          req.method,
          req.originalUrl,
          body,
          timestamp,
          nonce,
          signature
        );

        if (!isValid) {
          logger.warn('Invalid request signature');
          ResponseUtil.forbidden(res, 'Invalid request signature');
          return;
        }

        next();
      } catch (error) {
        logger.error('Request signature verification error:', error);
        ResponseUtil.forbidden(res, 'Signature verification failed');
      }
    };
  }
}

/**
 * Rate limiting middleware
 */
class RateLimiter {
  static clients = new Map();

  /**
   * Rate limiting middleware
   * @param {number} maxRequests - Maximum requests per window
   * @param {number} windowMs - Time window in milliseconds
   * @returns {Function} Middleware function
   */
  static limit(maxRequests, windowMs) {
    return (req, res, next) => {
      try {
        const clientId = req.ip || req.connection?.remoteAddress || 'unknown';
        const now = Date.now();

        const clientLimit = this.clients.get(clientId);
        
        if (!clientLimit || now > clientLimit.resetTime) {
          // Reset or initialize limit
          this.clients.set(clientId, {
            count: 1,
            resetTime: now + windowMs,
          });
          next();
          return;
        }

        if (clientLimit.count >= maxRequests) {
          logger.warn(`Rate limit exceeded for client: ${clientId}`);
          ResponseUtil.error(res, 'Rate limit exceeded', undefined, 429);
          return;
        }

        clientLimit.count++;
        next();
      } catch (error) {
        logger.error('Rate limiting error:', error);
        next();
      }
    };
  }
}

/**
 * Security headers middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const securityHeaders = (req, res, next) => {
  // Set security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'DENY');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');

  // Remove server information
  res.removeHeader('X-Powered-By');

  next();
};

/**
 * Input sanitization middleware
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next function
 */
const sanitizeInput = (req, res, next) => {
  try {
    // Sanitize request body
    if (req.body && typeof req.body === 'object') {
      req.body = sanitizeObject(req.body);
    }

    // Sanitize query parameters
    if (req.query && typeof req.query === 'object') {
      req.query = sanitizeObject(req.query);
    }

    // Sanitize URL parameters
    if (req.params && typeof req.params === 'object') {
      req.params = sanitizeObject(req.params);
    }

    next();
  } catch (error) {
    logger.error('Input sanitization error:', error);
    next();
  }
};

/**
 * Sanitize an object recursively
 * @param {any} obj - Object to sanitize
 * @returns {any} Sanitized object
 */
const sanitizeObject = (obj) => {
  if (obj === null || obj === undefined) {
    return obj;
  }

  if (typeof obj === 'string') {
    return sanitizeString(obj);
  }

  if (Array.isArray(obj)) {
    return obj.map(item => sanitizeObject(item));
  }

  if (typeof obj === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
      sanitized[sanitizeString(key)] = sanitizeObject(value);
    }
    return sanitized;
  }

  return obj;
};

/**
 * Sanitize a string to prevent XSS attacks
 * @param {string} str - String to sanitize
 * @returns {string} Sanitized string
 */
const sanitizeString = (str) => {
  if (typeof str !== 'string') {
    return str;
  }

  return str
    .replace(/[<>]/g, '') // Remove < and > characters
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers like onclick=
    .replace(/script/gi, '') // Remove script tags
    .trim();
};

export { CSRFProtection,
  RequestSigning,
  RateLimiter,
  securityHeaders,
  sanitizeInput
 };
