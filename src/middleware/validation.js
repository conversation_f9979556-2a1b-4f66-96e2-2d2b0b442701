import Joi from 'joi';
import { ResponseUtil } from '../utils/response.js';
import { VALIDATION_RULES, ERROR_MESSAGES } from '../utils/constants.js';
import logger from '../config/logger.js';

/**
 * Generic validation middleware factory
 */
export const validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body, { abortEarly: false });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      logger.warn(`Validation error: ${errorMessage}`);
      ResponseUtil.validationError(res, ERROR_MESSAGES.VALIDATION_ERROR, errorMessage);
      return;
    }

    next();
  };
};

/**
 * Validation schemas
 */
export const validationSchemas = {
  // Authentication schemas
  login: Joi.object({
    identifier: Joi.string()
      .required()
      .messages({
        'any.required': 'Email or mobile number is required',
        'string.empty': 'Email or mobile number cannot be empty',
      }),
    password: Joi.string()
      .min(VALIDATION_RULES.PASSWORD_MIN_LENGTH)
      .optional()
      .messages({
        'string.min': `Password must be at least ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters long`,
      }),
    requestOtp: Joi.boolean().optional(),
  }).custom((value, helpers) => {
    // Either password or requestOtp should be provided
    if (!value.password && !value.requestOtp) {
      return helpers.error('custom.passwordOrOtp');
    }
    return value;
  }).messages({
    'custom.passwordOrOtp': 'Either password or OTP request is required',
  }),

  register: Joi.object({
    email: Joi.string()
      .email()
      .optional()
      .messages({
        'string.email': 'Please provide a valid email address',
      }),
    mobile: Joi.string()
      .pattern(VALIDATION_RULES.MOBILE_REGEX)
      .optional()
      .messages({
        'string.pattern.base': 'Please provide a valid mobile number',
      }),
    password: Joi.string()
      .min(VALIDATION_RULES.PASSWORD_MIN_LENGTH)
      .required()
      .messages({
        'any.required': 'Password is required',
        'string.min': `Password must be at least ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters long`,
      }),
  }).custom((value, helpers) => {
    // Either email or mobile should be provided
    if (!value.email && !value.mobile) {
      return helpers.error('custom.emailOrMobile');
    }
    return value;
  }).messages({
    'custom.emailOrMobile': 'Either email or mobile number is required',
  }),

  requestOtp: Joi.object({
    identifier: Joi.string()
      .required()
      .custom((value, helpers) => {
        const isEmail = VALIDATION_RULES.EMAIL_REGEX.test(value);
        const isMobile = VALIDATION_RULES.MOBILE_REGEX.test(value);

        if (!isEmail && !isMobile) {
          return helpers.error('custom.invalidIdentifier');
        }

        return value;
      })
      .messages({
        'any.required': 'Email or mobile number is required',
        'custom.invalidIdentifier': 'Please provide a valid email or mobile number',
      }),
  }),

  verifyOtp: Joi.object({
    identifier: Joi.string()
      .required()
      .custom((value, helpers) => {
        const isEmail = VALIDATION_RULES.EMAIL_REGEX.test(value);
        const isMobile = VALIDATION_RULES.MOBILE_REGEX.test(value);

        if (!isEmail && !isMobile) {
          return helpers.error('custom.invalidIdentifier');
        }

        return value;
      })
      .messages({
        'any.required': 'Email or mobile number is required',
        'custom.invalidIdentifier': 'Please provide a valid email or mobile number',
      }),
    otp: Joi.string()
      .length(VALIDATION_RULES.OTP_LENGTH)
      .pattern(/^[0-9]+$/)
      .required()
      .messages({
        'any.required': 'OTP is required',
        'string.length': `OTP must be ${VALIDATION_RULES.OTP_LENGTH} digits`,
        'string.pattern.base': 'OTP must contain only numbers',
      }),
  }),

  verifyRegistration: Joi.object({
    identifier: Joi.string()
      .required()
      .custom((value, helpers) => {
        const isEmail = VALIDATION_RULES.EMAIL_REGEX.test(value);
        const isMobile = VALIDATION_RULES.MOBILE_REGEX.test(value);

        if (!isEmail && !isMobile) {
          return helpers.error('custom.invalidIdentifier');
        }

        return value;
      })
      .messages({
        'any.required': 'Email or mobile number is required',
        'custom.invalidIdentifier': 'Please provide a valid email or mobile number',
      }),
    otp: Joi.string()
      .length(VALIDATION_RULES.OTP_LENGTH)
      .pattern(/^[0-9]+$/)
      .required()
      .messages({
        'any.required': 'OTP is required',
        'string.length': `OTP must be ${VALIDATION_RULES.OTP_LENGTH} digits`,
        'string.pattern.base': 'OTP must contain only numbers',
      }),
  }),

  // Chat schemas
  chatMessage: Joi.object({
    message: Joi.string()
      .trim()
      .min(1)
      .max(4000)
      .required()
      .messages({
        'any.required': 'Message is required',
        'string.empty': 'Message cannot be empty',
        'string.min': 'Message cannot be empty',
        'string.max': 'Message is too long (maximum 4000 characters)',
      }),
    sessionId: Joi.string()
      .optional()
      .messages({
        'string.base': 'Session ID must be a string',
      }),
    llmModel: Joi.string()
      .optional()
      .messages({
        'string.base': 'LLM model must be a string',
      }),
  }),

  // Simplified chat message schema for streaming endpoint
  simpleChatMessage: Joi.object({
    message: Joi.string()
      .trim()
      .min(1)
      .max(4000)
      .required()
      .messages({
        'any.required': 'Message is required',
        'string.empty': 'Message cannot be empty',
        'string.min': 'Message cannot be empty',
        'string.max': 'Message is too long (maximum 4000 characters)',
      }),
    sessionId: Joi.string()
      .optional()
      .messages({
        'string.base': 'Session ID must be a string',
      }),
    llmModel: Joi.string()
      .optional()
      .default('gpt-3.5-turbo')
      .messages({
        'string.base': 'LLM model must be a string',
      }),
    hasAttachment: Joi.boolean()
      .optional()
      .default(false)
      .messages({
        'boolean.base': 'hasAttachment must be a boolean',
      }),
  }),

  // Chat message with attachment schema
  chatMessageWithAttachment: Joi.object({
    message: Joi.string()
      .trim()
      .min(1)
      .max(4000)
      .required()
      .messages({
        'any.required': 'Message is required',
        'string.empty': 'Message cannot be empty',
        'string.min': 'Message cannot be empty',
        'string.max': 'Message is too long (maximum 4000 characters)',
      }),
    sessionId: Joi.string()
      .optional()
      .messages({
        'string.base': 'Session ID must be a string',
      }),
    llmModel: Joi.string()
      .optional()
      .default('gpt-3.5-turbo')
      .messages({
        'string.base': 'LLM model must be a string',
      }),
  }),

  // Regenerate message schema
  regenerateMessage: Joi.object({
    messageId: Joi.string()
      .uuid()
      .required()
      .messages({
        'any.required': 'Message ID is required',
        'string.uuid': 'Message ID must be a valid UUID',
      }),
    llmModel: Joi.string()
      .optional()
      .default('gpt-3.5-turbo')
      .messages({
        'string.base': 'LLM model must be a string',
      }),
  }),

  // Pagination schema
  pagination: Joi.object({
    page: Joi.number()
      .integer()
      .min(1)
      .optional()
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be at least 1',
      }),
    limit: Joi.number()
      .integer()
      .min(1)
      .max(100)
      .optional()
      .default(10)
      .messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be at least 1',
        'number.max': 'Limit cannot exceed 100',
      }),
    offset: Joi.number()
      .integer()
      .min(0)
      .optional()
      .messages({
        'number.base': 'Offset must be a number',
        'number.integer': 'Offset must be an integer',
        'number.min': 'Offset must be at least 0',
      }),
  }),

  // Search query schema
  searchQuery: Joi.object({
    q: Joi.string()
      .trim()
      .min(1)
      .max(200)
      .required()
      .messages({
        'any.required': 'Search query is required',
        'string.empty': 'Search query cannot be empty',
        'string.min': 'Search query cannot be empty',
        'string.max': 'Search query is too long (maximum 200 characters)',
      }),
    page: Joi.number()
      .integer()
      .min(1)
      .optional()
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.min': 'Page must be at least 1',
      }),
    limit: Joi.number()
      .integer()
      .min(1)
      .max(50)
      .optional()
      .default(10)
      .messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.min': 'Limit must be at least 1',
        'number.max': 'Limit cannot exceed 50',
      }),
  }),

  // Chat ID schema
  chatId: Joi.object({
    chatId: Joi.string()
      .uuid()
      .required()
      .messages({
        'any.required': 'Chat ID is required',
        'string.uuid': 'Chat ID must be a valid UUID',
      }),
  }),
};

/**
 * Validate query parameters
 */
export const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error, value } = schema.validate(req.query, { abortEarly: false });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      logger.warn(`Query validation error: ${errorMessage}`);
      ResponseUtil.validationError(res, ERROR_MESSAGES.VALIDATION_ERROR, errorMessage);
      return;
    }

    // Replace query with validated values (Express 5.x compatible)
    Object.keys(req.query).forEach(key => delete req.query[key]);
    Object.assign(req.query, value);
    next();
  };
};

/**
 * Validate URL parameters
 */
export const validateParams = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.params, { abortEarly: false });

    if (error) {
      const errorMessage = error.details.map(detail => detail.message).join(', ');
      logger.warn(`Parameter validation error: ${errorMessage}`);
      ResponseUtil.validationError(res, ERROR_MESSAGES.VALIDATION_ERROR, errorMessage);
      return;
    }

    next();
  };
};

/**
 * Sanitize input data
 */
export const sanitizeInput = (req, res, next) => {
  try {
    // Basic sanitization for common fields
    if (req.body) {
      Object.keys(req.body).forEach(key => {
        if (typeof req.body[key] === 'string') {
          // Trim whitespace
          req.body[key] = req.body[key].trim();

          // Remove null bytes
          req.body[key] = req.body[key].replace(/\0/g, '');
        }
      });
    }

    next();
  } catch (error) {
    logger.error('Error sanitizing input:', error);
    next();
  }
};
